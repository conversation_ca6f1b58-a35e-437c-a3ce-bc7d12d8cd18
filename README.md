# Noah's NixOS Flakes Dotfiles

这是一个基于 Nix Flakes 和 Home Manager 的个人开发环境配置项目，专为开发者工作流程优化。

## 🚀 特性

- **模块化设计**: 将配置分解为独立的功能模块
- **跨平台支持**: 支持 Linux 系统（主要针对 x86_64-linux）
- **开发工具集成**: 包含完整的开发工具链
- **运维工具**: 集成 Kubernetes、Docker 等运维工具
- **中国镜像优化**: 使用清华大学镜像源加速下载

## 📁 项目结构

```
.
├── flake.nix          # 主要的 Flake 配置文件
├── flake.lock         # 锁定依赖版本
├── hosts.nix          # 主机配置信息
├── Makefile           # 便捷的管理命令
└── modules/           # 模块化配置
    ├── base/          # 基础工具和配置
    ├── dev/           # 开发工具
    ├── golang/        # Go 语言环境
    ├── nodejs/        # Node.js 环境
    ├── ops/           # 运维工具
    └── ssh/           # SSH 配置
```

## 🛠 包含的工具

### 基础工具 (base)
- **系统监控**: htop, btop, bottom, iotop, iftop
- **文件管理**: nnn, eza, fd, ripgrep, bat
- **网络工具**: mtr, iperf3, socat, nmap, dig
- **文本处理**: jq, yq-go, ack
- **其他**: fastfetch, tldr, ncdu, fzf, starship

### 开发工具 (dev)
- **构建工具**: go-task, pre-commit
- **HTTP 工具**: httpie, grpcurl
- **Nix 工具**: nixpkgs-fmt, nix-prefetch-git
- **环境管理**: direnv, nix-direnv
- **安全工具**: sops, eget

### 运维工具 (ops)
- **Kubernetes**: kubectl, k9s, velero
- **容器**: skopeo
- **部署**: deploy-rs
- **远程连接**: mosh

### 编程语言
- **Go**: 完整的 Go 开发环境
- **Node.js**: Node.js 运行时和包管理

## 🚀 快速开始

### 前置要求

确保你的系统已安装：
- Nix (启用 flakes 和 nix-command 实验性功能)
- Home Manager

### 安装

1. 克隆仓库：
```bash
git clone <repository-url> ~/.config/dotfiles
cd ~/.config/dotfiles
```

2. 应用配置：
```bash
make switch
```

或者使用详细模式进行调试：
```bash
make switch-debug
```

## 📋 管理命令

项目提供了便捷的 Makefile 命令：

```bash
# 应用配置更改
make switch

# 调试模式应用配置
make switch-debug

# 更新所有依赖
make update

# 查看系统历史
make history

# 垃圾回收（清理 7 天前的旧版本）
make gc
```

## 🏠 主机配置

`hosts.nix` 文件包含了预定义的主机配置：

- **云服务器**: fork (阿里云)
- **家庭实验室**: potato, batata
- **虚拟机**: tofu, pizza-master

## 🔧 自定义配置

### 添加新模块

1. 在 `modules/` 目录下创建新的模块目录
2. 创建 `default.nix` 文件定义模块配置
3. 在 `flake.nix` 中的 modules 列表中添加新模块

### 修改现有配置

每个模块都有独立的配置文件，可以根据需要进行修改：

- `modules/base/`: 基础工具和 shell 配置
- `modules/dev/`: 开发工具和环境
- `modules/ops/`: 运维和部署工具

## 🌏 中国用户优化

项目针对中国用户进行了优化：
- 使用清华大学 Nix 镜像源
- 集成了 nali 等本地化工具
- 支持 ArchLinux 下的 paru 包管理器

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个配置！

## 📄 许可证

本项目采用开源许可证，具体请查看 LICENSE 文件。
