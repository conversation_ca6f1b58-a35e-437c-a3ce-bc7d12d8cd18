{
  description = "<PERSON>'s NixOS Flakes of Devbox";

  nixConfig = {
    experimental-features = [ "nix-command" "flakes" ];
    substituters = [
      # replace official cache with a mirror located in China
      "https://mirrors.tuna.tsinghua.edu.cn/nix-channels/store"
      "https://cache.nixos.org/"
    ];

    # nix community's cache server
    extra-substituters = [
      "https://nix-community.cachix.org"
    ];
    extra-trusted-public-keys = [
      "nix-community.cachix.org-1:mB9FSh9qf2dCimDSUo8Zy7bkq5CX+/rkCWyvRCYg3Fs="
    ];
  };

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";

    home-manager = {
      url = "github:nix-community/home-manager";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    agenix = {
      url = "github:ryantm/agenix";
      inputs.nixpkgs.follows = "nixpkgs";
    };

    flake-utils.url = "github:numtide/flake-utils";
    pre-commit-hooks.url = "github:cachix/pre-commit-hooks.nix";
  };

  outputs =
    { self
    , nixpkgs
    , pre-commit-hooks
    , flake-utils
    , home-manager
    , agenix
    , ...
    }@inputs:
    {
      # nixosConfigurations = import ./wsl inputs;
      homeConfigurations = builtins.mapAttrs
        (
          username: _: home-manager.lib.homeManagerConfiguration {
            pkgs = import nixpkgs {
              system = "x86_64-linux";
              config.allowUnfree = true;
            };

            modules = [
              {
                home.username = "${username}";
                home.homeDirectory = "/home/<USER>";

                home.packages = [
                  agenix.packages."x86_64-linux".default
                ];

                home.activation =
                  {
                    # Only for ArchLinux
                    paru = home-manager.lib.hm.dag.entryAfter [ "writeBoundary" ] ''
                      run command -v paru > /dev/null && paru -S --noconfirm --needed \
                        python terraform
                    '';
                  };
              }
              ./modules/base
              ./modules/ssh
              ./modules/ops
              ./modules/dev
              ./modules/nodejs
              ./modules/golang
            ];
          }
        )
        {
          # Homelab node with noah user
          noah = { };
        };
    } // flake-utils.lib.eachDefaultSystem
      (system: {
        checks = {
          pre-commit-check = pre-commit-hooks.lib.${system}.run {
            src = ./.;
            hooks = {
              nixpkgs-fmt.enable = true;
            };
          };
        };
        devShells.default = nixpkgs.legacyPackages.${system}.mkShell {
          inherit (self.checks.${system}.pre-commit-check) shellHook;
        };
      });
}
