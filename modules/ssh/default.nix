{ config, lib, pkgs, ... }:
let
  inherit (builtins) mapAttrs;

  nodeMap = {
    wok = {
      hostname = "************";
      user = "root";
      extraOptions = {
        "PubkeyAcceptedAlgorithms" = "+ssh-rsa";
        "HostkeyAlgorithms" = "+ssh-rsa";
      };
    };
  } // (import ../../hosts.nix);
in
{
  programs.ssh = {
    enable = true;
    forwardAgent = true;
    serverAliveInterval = 60;

    matchBlocks = mapAttrs
      (nodeName: nodeConfig: {
        hostname = "${nodeName}.node.noahgao.net";
        user = "noah";
      } // nodeConfig)
      nodeMap;
  };
}
