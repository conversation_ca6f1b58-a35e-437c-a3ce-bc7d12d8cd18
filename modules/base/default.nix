{ lib, pkgs, ... }:
{
  # Let Home Manager install and manage itself.
  programs.home-manager.enable = true;

  home.stateVersion = "24.05";

  home.packages = with pkgs; [
    procs
    fastfetch
    jq
    yq-go
    tldr
    nnn
    ack
    ncdu

    bottom
    btop
    iotop
    iftop

    mtr
    iperf3
    socat
    nmap
    nali
    dig
    dogdns
    strace
    ltrace
    lsof
  ];

  home.sessionVariables = {
    EDITOR = "nvim";
  };

  programs.starship.enable = true;

  programs.eza = {
    enable = true;
    icons = "auto";
    git = true;
  };

  programs.neovim = {
    enable = true;
    defaultEditor = true;
    viAlias = true;
    vimAlias = true;
  };

  programs.htop.enable = true;
  programs.pay-respects.enable = true;
  programs.fzf.enable = true;
  programs.broot.enable = true;
  programs.bat.enable = true;
  programs.fd.enable = true;
  programs.ripgrep.enable = true;

  imports = [
    ./fish.nix
    ./git.nix
  ];
}
