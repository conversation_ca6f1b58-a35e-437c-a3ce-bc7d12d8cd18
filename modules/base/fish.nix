{ config, lib, pkgs, ... }:

{
  home.packages = with pkgs; [
    grc # Colourized CLI Util
  ];

  programs.fish = {
    enable = true;
    plugins = [
      {
        name = "z";
        src = pkgs.fishPlugins.z.src;
      }
      {
        name = "fzf-fish";
        src = pkgs.fishPlugins.fzf-fish.src;
      }
      {
        name = "fifc";
        src = pkgs.fishPlugins.fifc.src;
      }
      {
        name = "autopair";
        src = pkgs.fishPlugins.autopair.src;
      }
      {
        name = "grc";
        src = pkgs.fishPlugins.grc.src;
      }
      {
        name = "git-abbr";
        src = pkgs.fishPlugins.git-abbr.src;
      }
      {
        name = "done";
        src = pkgs.fishPlugins.done.src;
      }
      {
        name = "foreign-env";
        src = pkgs.fetchFromGitHub {
          owner = "oh-my-fish";
          repo = "plugin-foreign-env";
          rev = "7f0cf099ae1e1e4ab38f46350ed6757d54471de7";
          sha256 = "4+k5rSoxkTtYFh/lEjhRkVYa2S4KEzJ/IJbyJl+rJjQ=";
        };
      }
    ];

    functions = {
      toggle_proxy = {
        description = "Toggle proxy";
        body = ''
          if test -z "$http_proxy";
            set -Ux http_proxy "$PROXY_HOST"
            set -Ux https_proxy "$PROXY_HOST"
            set -Ux no_proxy "git.noahgao.net,localhost"
            echo "proxy on with $http_proxy"
          else;
            set -e http_proxy
            set -e https_proxy
            echo "proxy off"
          end
        '';
      };
    };

    shellAliases = {
      k = "kubectl";
      proxy = "toggle_proxy";
      lg = "lazygit";
    };

    interactiveShellInit = ''
      if [ "$TERM_PROGRAM" = "vscode" ]
        set -U fish_greeting ""; # Empty output in VSCode Integrated Terminal
      else
        set -U fish_greeting "🐟 Hello from fish shell."
      end

      # Nix
      [ -f ~/.nix-profile/etc/profile.d/nix.sh ]; and fenv source ~/.nix-profile/etc/profile.d/nix.sh; or true
      [ -f ~/.nix-profile/etc/profile.d/hm-session-vars.sh ]; and fenv source ~/.nix-profile/etc/profile.d/hm-session-vars.sh; or true

      # Node.js
      fnm env --use-on-cd --shell fish | source
      fish_add_path $PNPM_HOME

      # fifc
      set -Ux fifc_editor nvim
      set -U fifc_keybinding \cx

      # fzf.fish
      fzf_configure_bindings --history=\cr # override fzf default
    '';
  };

  xdg.configFile = {
    "fish/conf.d/nix.fish".text = ''
      if test -e '/etc/profile.d/nix-daemon.fish'
        . /etc/profile.d/nix-daemon.fish
      end
    '';
    "fish/completions/pnpm.fish".source = ./completions/pnpm.fish;
  };
}
