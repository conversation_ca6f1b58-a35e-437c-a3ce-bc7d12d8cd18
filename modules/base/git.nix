{ lib, pkgs, ... }:
{
  home.packages = with pkgs; [
    ghq # Code Repo Management
  ];

  programs.git = {
    enable = true;
    userName = "Noah Gao";
    userEmail = "<EMAIL>";
    extraConfig = {
      alias = {
        undo = "reset --soft HEAD^";
      };

      log = {
        decorate = "short";
        abbrevCommit = "true";
      };
      init.defaultBranch = "master";
      ghq.root = "~/Code";
    };
  };

  programs.lazygit = {
    enable = true;
    settings = {
      gui.showIcons = true;
    };
  };
}
