{ lib, pkgs, ... }:
{
  home.packages = with pkgs; [
    go-task
    pre-commit
    eget
    sops
    httpie
    grpcurl
    nali

    # Nix
    nix-prefetch-git
    nix-prefetch-github
    prefetch-npm-deps
    nixpkgs-fmt
  ];

  home.file = {
    ".eget.toml".text = ''
      [global]
      target = "~/.local/bin"
    '';
  };

  programs.direnv = {
    enable = true;
    nix-direnv.enable = true;
  };

  xdg.configFile = {
    "direnv/direnvrc".source = ./direnvrc;
  };
}
